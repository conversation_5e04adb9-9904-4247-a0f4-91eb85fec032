// Test file for dual currency payment logic
const { processDualCurrencyPayment } = require('./dual-currency-payment-logic');

// Mock data for testing
const mockApp = {
    collection: (name) => ({
        get: async (id) => {
            if (name === 'accounting.pos') {
                return {
                    currencyId: 'USD',
                    splitCreditCardSlips: true,
                    paymentOnSpecificDate: false,
                    installmentPayments: [
                        { installment: 1, refund: 0 },
                        { installment: 2, refund: 30 },
                        { installment: 3, refund: 60 }
                    ]
                };
            }
        }
    }),
    datetime: {
        fromJSDate: (date) => ({
            startOf: () => ({
                plus: (obj) => ({
                    day: 15,
                    plus: (obj2) => ({ set: (obj3) => ({ toJSDate: () => new Date() }) }),
                    set: (obj3) => ({ toJSDate: () => new Date() })
                })
            })
        })
    }
};

const mockCompany = {
    currencyId: 'TRY'
};

const mockJournal = {
    debitAccountId: 'account123',
    currencyId: 'TRY'
};

const mockPartner = {
    _id: 'partner123'
};

const mockEntry = {
    issueDate: new Date()
};

// Test case 1: POS payment with both TL and USD amounts
async function testDualCurrencyPOS() {
    console.log('=== Test 1: POS Payment with Dual Currency ===');
    
    const payload = {
        documentType: 'pos',
        posId: 'pos123',
        currencyId: 'USD',
        evaluateAccountCurrency: false,
        tutar: 1000, // TL amount
        tutar_sbp: 30, // USD amount
        total: 1000,
        installmentCount: 3,
        installmentAmount: 333.33,
        currencyRate: 33.33, // 1 USD = 33.33 TL
        globalCurrencyRate: 1,
        description: 'Test payment',
        branchId: 'branch123',
        financialProjectId: 'project123',
        cashFlowItemId: 'cashflow123',
        documentNo: 'DOC001',
        partnerType: 'customer',
        partnerId: 'partner123',
        contactPersonId: 'contact123',
        journalId: 'journal123',
        paymentAccountId: 'payaccount123',
        recordDate: new Date(),
        issueDate: new Date(),
        dueDate: new Date(),
        reference: 'REF001',
        baseTotal: 1000,
        dueDifference: 0,
        cardBrand: 'visa',
        cardHolder: 'John Doe',
        cardNumber: '****************',
        expireMonth: 12,
        expireYear: 2025,
        cvv: '123',
        issuedBy: 'bank123',
        tagIds: []
    };

    try {
        // Mock global variables that would be available in your actual code
        global.journal = mockJournal;
        global.partner = mockPartner;
        global.entry = mockEntry;
        
        const result = await processDualCurrencyPayment(payload, mockApp, mockCompany);
        
        console.log('Journal Items:', JSON.stringify(result.items, null, 2));
        console.log('Slips:', JSON.stringify(result.slips, null, 2));
        
        // Verify results
        console.log('\n--- Verification ---');
        console.log('Number of journal items:', result.items.length);
        console.log('Number of slips:', result.slips.length);
        
        if (result.items.length > 0) {
            const firstItem = result.items[0];
            console.log('First journal item currency:', firstItem.currencyId);
            console.log('First journal item debitFC:', firstItem.debitFC);
            console.log('First journal item debit:', firstItem.debit);
            console.log('Original TL amount:', firstItem.originalAmountTL);
            console.log('Original USD amount:', firstItem.originalAmountUSD);
        }
        
        if (result.slips.length > 0) {
            const firstSlip = result.slips[0];
            console.log('First slip amount (TL):', firstSlip.amount);
            console.log('First slip amount (USD):', firstSlip.amountUSD);
            console.log('First slip installment amount:', firstSlip.installmentAmount);
        }
        
    } catch (error) {
        console.error('Test failed:', error.message);
    }
}

// Test case 2: Non-POS payment with dual currency
async function testDualCurrencyNonPOS() {
    console.log('\n=== Test 2: Non-POS Payment with Dual Currency ===');
    
    const payload = {
        documentType: 'invoice',
        currencyId: 'USD',
        evaluateAccountCurrency: true,
        tutar: 500, // TL amount
        tutar_sbp: 15, // USD amount
        total: 500,
        currencyRate: 33.33,
        globalCurrencyRate: 1,
        description: 'Invoice payment',
        branchId: 'branch123',
        financialProjectId: 'project123',
        cashFlowItemId: 'cashflow123',
        scope: '1'
    };

    try {
        global.journal = mockJournal;
        global.partner = mockPartner;
        
        const result = await processDualCurrencyPayment(payload, mockApp, mockCompany);
        
        console.log('Journal Items:', JSON.stringify(result.items, null, 2));
        
        // Verify results
        console.log('\n--- Verification ---');
        console.log('Number of journal items:', result.items.length);
        
        if (result.items.length > 0) {
            const item = result.items[0];
            console.log('Journal item currency:', item.currencyId);
            console.log('Journal item debitFC:', item.debitFC);
            console.log('Journal item debit:', item.debit);
            console.log('Original TL amount:', item.originalAmountTL);
            console.log('Original USD amount:', item.originalAmountUSD);
        }
        
    } catch (error) {
        console.error('Test failed:', error.message);
    }
}

// Run tests
async function runTests() {
    await testDualCurrencyPOS();
    await testDualCurrencyNonPOS();
}

// Export for use in other files
module.exports = {
    testDualCurrencyPOS,
    testDualCurrencyNonPOS,
    runTests
};

// Run tests if this file is executed directly
if (require.main === module) {
    runTests();
}
