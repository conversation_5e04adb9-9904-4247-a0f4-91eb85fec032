# Dual Currency Payment System Implementation

Bu dokümantasyon, mevcut make-payment backend'inizi dual currency (TL ve USD) sistemine nasıl güncelledik ve nasıl çalıştığını açıklar.

## Ya<PERSON><PERSON><PERSON> Değişiklikler

### 1. Frontend Değişiklikleri

#### BalanceGroup.tsx
- USD input alanı eklendi
- Exchange rate fetch edildi
- TL ve USD arasında otomatik dönüşüm
- Context'e hem TL hem USD miktarları gönderiliyor

#### Payment Context
- `selectedBalanceUSD` state eklendi
- `exchangeRate` state eklendi
- Dual currency bilgileri payload'a eklendi

#### PaymentCreditCard
- `amountUSD` ve `exchangeRate` payload'a eklendi

### 2. API Değişiklikleri

#### makePayment.ts
```typescript
const paymentSchema = validator.object({
    amount: validator.number().required(),        // TL miktarı
    amountUSD: validator.number().optional(),     // USD miktarı
    exchangeRate: validator.number().optional(),  // Döviz kuru
    installmentCount: validator.number().required(),
    // ... diğer alanlar
});

// Backend'e gönderilen payload
const backendPayload = {
    ...payload,
    customerId: customer?.id,
    tutar: payload.amount,           // TL miktarı
    tutar_sbp: payload.amountUSD,    // USD miktarı (SBP)
    currencyRate: payload.exchangeRate || 1,
    globalCurrencyRate: 1,
    currencyId: payload.amountUSD && payload.amountUSD > 0 ? 'USD' : 'TRY',
    evaluateAccountCurrency: payload.amountUSD && payload.amountUSD > 0
};
```

### 3. Backend Değişiklikleri

#### updated-make-payment-backend.js

**Yeni Request Body Alanları:**
```javascript
const {
    customerId,
    amount,           // TL miktarı
    amountUSD,        // USD miktarı
    exchangeRate,     // Döviz kuru
    installmentCount,
    // ... diğer alanlar
} = request.body;
```

**Dual Currency Logic:**
```javascript
// Hangi para biriminin kullanılacağını belirle
let finalAmount = amount;
let isUsingForeignCurrency = false;
let currencyId = store.currencyId;
let currencyRate = 1;

if (amount && amountUSD && exchangeRate) {
    if (store.currencyId !== 'TRY' && amountUSD > 0) {
        // USD kullan
        finalAmount = amountUSD;
        currencyId = 'USD';
        currencyRate = exchangeRate;
        isUsingForeignCurrency = true;
    } else {
        // TL kullan
        finalAmount = amount;
        currencyId = store.currencyId;
        currencyRate = 1;
    }
}
```

**Enhanced Entry Object:**
```javascript
entry.amount = amount || finalAmount;        // TL miktarı
entry.amountUSD = amountUSD;                // USD miktarı
entry.currencyId = currencyId;              // Kullanılan para birimi
entry.currencyRate = currencyRate;          // Döviz kuru
entry.exchangeRateUsed = exchangeRate;      // Kullanılan döviz kuru
entry.isUsingForeignCurrency = isUsingForeignCurrency;
```

**Enhanced Integration Data:**
```javascript
const data = {
    // ... mevcut alanlar
    tutar: amount,                    // TL miktarı
    tutar_sbp: amountUSD,            // USD miktarı (SBP)
    exchangeRateUsed: exchangeRate,
    isUsingForeignCurrency: isUsingForeignCurrency,
    evaluateAccountCurrency: isUsingForeignCurrency,
    globalCurrencyRate: 1,
    // ...
};
```

## Kullanım Senaryoları

### Senaryo 1: Sadece TL ile Ödeme
```javascript
{
    amount: 1000,        // 1000 TL
    amountUSD: null,
    exchangeRate: null
}
// Sonuç: 1000 TL ile işlem yapılır
```

### Senaryo 2: Sadece USD ile Ödeme
```javascript
{
    amount: null,
    amountUSD: 30,       // 30 USD
    exchangeRate: 33.33
}
// Sonuç: 30 USD ile işlem yapılır
```

### Senaryo 3: Hem TL hem USD (Dual Input)
```javascript
{
    amount: 1000,        // 1000 TL
    amountUSD: 30,       // 30 USD
    exchangeRate: 33.33  // 1 USD = 33.33 TL
}
// Sonuç: Store currency'ye göre uygun olan kullanılır
```

## Database Schema Önerileri

### Financial Entry Table
```sql
ALTER TABLE financial_entries ADD COLUMN amount_usd DECIMAL(15,2);
ALTER TABLE financial_entries ADD COLUMN exchange_rate_used DECIMAL(10,4);
ALTER TABLE financial_entries ADD COLUMN is_using_foreign_currency BOOLEAN DEFAULT FALSE;
```

### POS Slips Table
```sql
ALTER TABLE pos_slips ADD COLUMN amount_usd DECIMAL(15,2);
ALTER TABLE pos_slips ADD COLUMN exchange_rate_used DECIMAL(10,4);
```

## Test Senaryoları

### Test 1: Frontend'de Dual Input
1. BalanceGroup'ta TL miktarı gir
2. USD miktarı otomatik hesaplansın
3. Veya USD miktarı gir, TL otomatik hesaplansın
4. Payment'ta her iki miktar da backend'e gönderilsin

### Test 2: Backend'de Currency Selection
1. Hem TL hem USD gönderildiğinde doğru currency seçilsin
2. Installment hesaplaması doğru currency ile yapılsın
3. Entry ve slip'lerde her iki miktar da saklansin

### Test 3: Integration Data
1. POS integration'a doğru currency bilgileri gönderilsin
2. `tutar` ve `tutar_sbp` alanları doğru doldurulsun
3. Exchange rate bilgisi korunsin

## Avantajlar

1. **Şeffaflık**: Kullanıcı hem TL hem USD miktarını görebilir
2. **Esneklik**: İstediği currency ile ödeme yapabilir
3. **Kayıt Tutma**: Her iki miktar da database'de saklanır
4. **Raporlama**: Dual currency raporları çıkarılabilir
5. **Uyumluluk**: Mevcut sistem ile uyumlu, geriye dönük uyumlu

## Dikkat Edilmesi Gerekenler

1. **Exchange Rate Güncelliği**: Döviz kurlarının güncel olması
2. **Validation**: Her iki miktarın tutarlı olması
3. **Rounding**: Döviz çevriminde yuvarlama hatalarına dikkat
4. **Performance**: Exchange rate API çağrılarının optimize edilmesi
5. **Error Handling**: Döviz kuru alınamadığında fallback mekanizması
