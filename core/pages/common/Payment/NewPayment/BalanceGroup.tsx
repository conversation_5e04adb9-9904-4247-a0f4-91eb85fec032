import {useMemo, useState, useEffect, useRef} from 'react';
import {useRouter} from 'next/router';
import CurrencyInput from 'react-currency-input-field';
import {cls, debounce} from '@core/helpers';
import {useTrans} from '@core/hooks';
import {UiRadioGroup} from '@core/components/ui';
import {CheckCircleIcon} from '@core/icons/solid';
import Price from '@components/common/Price';
import usePayment from './context';

type PaymentPlan = 'partial' | 'total' | null;

const BalanceGroup = () => {
    const t = useTrans();
    const router = useRouter();

    const [paymentPlan, setPaymentPlan] = useState<PaymentPlan>(null);

    const {
        balance,
        isLoading,
        setSelectedBalance,
        setSelectedBalanceUSD,
        exchangeRate,
        setExchangeRate
    } = usePayment();

    // State for input display values
    const [tlAmount, setTlAmount] = useState<string>('');
    const [usdAmount, setUsdAmount] = useState<string>('');

    // Fetch exchange rates (USD) with POST as user described
    useEffect(() => {
        const fetchExchangeRates = async () => {
            try {
                const response = await fetch('/api/customers/exchange-rates', {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'}
                });
                const data = await response.json();
                if (data.status === 'success' && Array.isArray(data.data)) {
                    const usd = data.data.find(
                        (c: any) => c.currencyName === 'USD'
                    );
                    if (usd && usd.banknoteSelling) {
                        setExchangeRate(usd.banknoteSelling);
                    }
                }
            } catch (e) {
                setExchangeRate(null);
            }
        };
        fetchExchangeRates();
    }, []);

    const priceFormatter = useMemo(() => {
        return Intl.NumberFormat(router.locale, {
            style: 'decimal',
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        });
    }, [router.locale]);

    // Prevent infinite loop: track which input is being edited
    const editingRef = useRef<'tl' | 'usd' | null>(null);

    // TL input change handler (updates USD)
    const handleTlChange = (value: string | undefined) => {
        editingRef.current = 'tl';
        if (value === undefined || value === '') {
            setTlAmount('');
            setUsdAmount('');
            setSelectedBalance(0);
            setSelectedBalanceUSD(0);
            return;
        }
        setTlAmount(value);
        const tl = parseFloat(value.replace(',', '.'));
        const validTl = isNaN(tl) ? 0 : tl;
        setSelectedBalance(validTl);

        console.log('TL Change - TL:', validTl, 'Exchange Rate:', exchangeRate);

        if (exchangeRate && !isNaN(tl) && tl > 0) {
            const usd = tl / exchangeRate;
            setUsdAmount(usd.toFixed(2));
            setSelectedBalanceUSD(isNaN(usd) ? 0 : usd);
            console.log('TL Change - USD:', usd);
        } else {
            setUsdAmount('');
            setSelectedBalanceUSD(0);
        }
    };

    // USD input change handler (updates TL)
    const handleUsdChange = (value: string | undefined) => {
        editingRef.current = 'usd';
        if (value === undefined || value === '') {
            setUsdAmount('');
            setTlAmount('');
            setSelectedBalance(0);
            setSelectedBalanceUSD(0);
            return;
        }
        setUsdAmount(value);
        const usd = parseFloat(value.replace(',', '.'));
        const validUsd = isNaN(usd) ? 0 : usd;
        setSelectedBalanceUSD(validUsd);

        console.log(
            'USD Change - USD:',
            validUsd,
            'Exchange Rate:',
            exchangeRate
        );

        if (exchangeRate && !isNaN(usd) && usd > 0) {
            const tl = usd * exchangeRate;
            setTlAmount(tl.toFixed(2));
            setSelectedBalance(isNaN(tl) ? 0 : tl);
            console.log('USD Change - TL:', tl);
        } else {
            setTlAmount('');
            setSelectedBalance(0);
        }
    };

    // Keep inputs in sync only when the other is edited
    useEffect(() => {
        if (!exchangeRate) return;
        if (editingRef.current === 'tl') {
            // TL changed, USD already set
            editingRef.current = null;
        } else if (editingRef.current === 'usd') {
            // USD changed, TL already set
            editingRef.current = null;
        }
    }, [tlAmount, usdAmount, exchangeRate]);

    return (
        <UiRadioGroup value={paymentPlan} onChange={setPaymentPlan}>
            <div className="grid gap-4 lg:grid-cols-2">
                <UiRadioGroup.Option
                    value="total"
                    onClick={() => setSelectedBalance(balance)}
                    disabled={balance <= 0 || isLoading}
                    className={({checked}) =>
                        cls(
                            checked && 'border-transparent',
                            'shadow-small relative w-full cursor-pointer rounded-lg border bg-white p-5 focus:outline-none disabled:cursor-not-allowed disabled:opacity-40'
                        )
                    }
                >
                    {({checked}) => (
                        <div className="flex items-center">
                            {checked ? (
                                <CheckCircleIcon className="mr-4 h-7 w-7 text-primary-600" />
                            ) : (
                                <div className="mr-4 h-7 w-7 rounded-full border"></div>
                            )}

                            <div className="flex flex-1">
                                <div className="flex flex-1 flex-col">
                                    <UiRadioGroup.Label
                                        as="span"
                                        className="block text-sm font-medium"
                                    >
                                        {t('Balance')}
                                    </UiRadioGroup.Label>

                                    <UiRadioGroup.Description
                                        as="span"
                                        className="mt-1 flex items-center text-sm text-muted"
                                    >
                                        {t('Pay all the balance')}
                                    </UiRadioGroup.Description>
                                </div>

                                <div className="ml-4 self-center text-sm font-medium">
                                    <UiRadioGroup.Description>
                                        {isLoading ? (
                                            <span className="skeleton-card block h-6 w-24"></span>
                                        ) : (
                                            <Price price={balance} />
                                        )}
                                    </UiRadioGroup.Description>
                                </div>
                            </div>

                            <div
                                className={cls(
                                    checked
                                        ? 'border-primary-600'
                                        : 'border-transparent',
                                    'pointer-events-none absolute -inset-px rounded-md border-2'
                                )}
                            />
                        </div>
                    )}
                </UiRadioGroup.Option>

                <UiRadioGroup.Option
                    value="partial"
                    onClick={() => {
                        if (paymentPlan === 'total') setSelectedBalance(0);
                    }}
                    disabled={isLoading}
                    className={({checked}) =>
                        cls(
                            checked && 'border-transparent',
                            'shadow-small relative w-full cursor-pointer rounded-lg border bg-white p-5 focus:outline-none disabled:cursor-not-allowed disabled:opacity-40'
                        )
                    }
                >
                    {({checked}) => (
                        <div className="flex items-center">
                            {checked ? (
                                <CheckCircleIcon className="mr-4 h-7 w-7 text-primary-600" />
                            ) : (
                                <div className="mr-4 h-7 w-7 rounded-full border"></div>
                            )}

                            <div className="flex flex-1 flex-col max-md:gap-2 md:flex-row">
                                <div className="flex flex-1 flex-col">
                                    <UiRadioGroup.Label
                                        as="span"
                                        className="block text-sm font-medium"
                                    >
                                        {t('Pay Amount')}
                                    </UiRadioGroup.Label>

                                    <UiRadioGroup.Description
                                        as="span"
                                        className="mt-1 flex items-center text-sm text-muted"
                                    >
                                        {t(
                                            'Enter how much you are willing to pay'
                                        )}
                                    </UiRadioGroup.Description>
                                </div>

                                {checked && (
                                    <div className="flex flex-col gap-2 text-sm font-medium md:ml-4 md:self-center">
                                        <UiRadioGroup.Description as="div">
                                            {/* TL input */}
                                            <CurrencyInput
                                                placeholder={`${priceFormatter.format(
                                                    balance
                                                )} TL`}
                                                decimalsLimit={2}
                                                decimalScale={2}
                                                allowNegativeValue={false}
                                                disableAbbreviations
                                                allowDecimals
                                                suffix=" TL"
                                                min={0.01}
                                                className="w-32 rounded-lg !border-secondary-300 text-sm focus:border-primary-600 focus:ring-2 focus:!ring-primary-600"
                                                value={tlAmount}
                                                onValueChange={handleTlChange}
                                                disabled={!exchangeRate}
                                            />
                                        </UiRadioGroup.Description>
                                        {/* USD input */}
                                        <div className="mt-1 flex items-center gap-2">
                                            <CurrencyInput
                                                placeholder={
                                                    exchangeRate
                                                        ? `USD (YBP)`
                                                        : t('Loading...')
                                                }
                                                decimalsLimit={2}
                                                decimalScale={2}
                                                allowNegativeValue={false}
                                                disableAbbreviations
                                                allowDecimals
                                                prefix="$ "
                                                min={0.01}
                                                className="w-32 rounded-lg !border-secondary-300 text-sm focus:border-primary-600 focus:ring-2 focus:!ring-primary-600"
                                                value={usdAmount}
                                                onValueChange={handleUsdChange}
                                                disabled={!exchangeRate}
                                            />
                                        </div>
                                    </div>
                                )}
                            </div>

                            <div
                                className={cls(
                                    checked
                                        ? 'border-primary-600'
                                        : 'border-transparent',
                                    'pointer-events-none absolute -inset-px rounded-md border-2'
                                )}
                            />
                        </div>
                    )}
                </UiRadioGroup.Option>
            </div>
        </UiRadioGroup>
    );
};

export default BalanceGroup;
