import {memo, useCallback, useEffect, useState} from 'react';
import {isDev, jsonRequest, trim} from '@core/helpers';
import {useTrans, useUI} from '@core/hooks';
import CardBox from './CardBox';
import Form from './Form';
import usePayment, {Installment} from '../context';

const PaymentCreditCard = memo(() => {
    const t = useTrans();

    const {openModal} = useUI();
    const {
        setProcessPayment,
        installments,
        selectedBalance,
        selectedBalanceUSD,
        exchangeRate,
        setCardBrandCode
    } = usePayment();

    const [cardBrand, setCardBrand] = useState('none');
    const [cardBrandLogo, setCardBrandLogo] = useState<string>();
    const [bankLogo, setBankLogo] = useState<string>();
    const [cardSchemaLogo, setCardSchemaLogo] = useState<string>();
    const [cardNumber, setCardNumber] = useState<string>('');
    const [cardHolder, setCardHolder] = useState<string>('');
    const [cardExpiry, setCardExpiry] = useState<string>('');
    const [cardCvv, setCardCvv] = useState<string>('');
    const [focussedField, setFocussedField] = useState<string>();

    let selectedPaymentPlan: Installment['installments'][0] | undefined;
    const selectedCard = installments.find(
        installment =>
            (selectedPaymentPlan = installment.installments.find(
                i => i.isChecked
            ))
    );

    useEffect(() => {
        setProcessPayment(async () => {
            const payload: any = {
                cardBrand,
                cardNumber: '',
                cardHolder: '',
                expireMonth: 1,
                expireYear: new Date().getFullYear(),
                cvv: '',
                installmentCount: 1,
                amount: 0,
                amountUSD: 0,
                exchangeRate: null,
                selectedCardBrand: selectedCard?.cardBrandCode ?? ''
            };

            payload.cardNumber = trim(
                cardNumber.replaceAll('-', '').replaceAll(' ', '')
            );
            if (payload.cardNumber.length !== 16) {
                throw new Error(t('Card number is invalid'));
            }

            payload.cardHolder = trim(cardHolder);
            if (payload.cardHolder.length < 3) {
                throw new Error(t('Card holder is invalid'));
            }

            const expiryStr = trim(
                cardExpiry
                    .replaceAll(' ', '')
                    .replaceAll('-', '')
                    .replaceAll('/', '')
            );
            if (expiryStr.length < 4) {
                throw new Error(t('Card expiry is invalid'));
            }
            const month = parseInt(expiryStr.slice(0, 2));
            if (!(month >= 1 && month <= 12)) {
                throw new Error(t('Card expiry is invalid'));
            }
            const currentYearStr = new Date().getFullYear().toString();
            const year = parseInt(
                `${currentYearStr.slice(0, 2)}${expiryStr.slice(2)}`
            );
            payload.expireMonth = month;
            payload.expireYear = year;

            payload.cvv = trim(cardCvv.replaceAll('-', '').replaceAll(' ', ''));
            if (payload.cvv.length < 3) {
                throw new Error(t('Card cvv is invalid'));
            }
            if (
                selectedCard?.cardBrandCode !== cardBrand &&
                (selectedPaymentPlan?.installmentCount ?? 1) > 1
            ) {
                throw new Error(
                    t(
                        'The POS installment rates do not match with the card you have selected. Please select single installment for this card brand.'
                    )
                );
            }

            // Validate amounts
            if (
                selectedBalance < 0.01 &&
                (!selectedBalanceUSD || selectedBalanceUSD < 0.01)
            ) {
                throw new Error(t('Payment amount must be at least 0.01'));
            }

            payload.amount = Math.round(selectedBalance * 100) / 100;
            payload.amountUSD = selectedBalanceUSD
                ? Math.round(selectedBalanceUSD * 100) / 100
                : null;
            payload.exchangeRate = exchangeRate;
            payload.installmentCount =
                selectedPaymentPlan?.installmentCount ?? 1;

            console.log('PaymentCreditCard - Final payload:', payload);
            return payload;
        });
    }, [
        cardBrand,
        cardCvv,
        cardExpiry,
        cardHolder,
        cardNumber,
        selectedCard,
        selectedPaymentPlan,
        selectedBalance,
        selectedBalanceUSD,
        exchangeRate,
        openModal,
        setProcessPayment,
        t
    ]);

    const onChange = useCallback(
        (field: string, value: string) => {
            if (field === 'cardNumber') {
                if (value !== cardNumber) {
                    const normalizedCardNumber = value
                        .replaceAll(' ', '')
                        .replaceAll('-', '');

                    if (normalizedCardNumber.length === 16) {
                        (async () => {
                            try {
                                const {info} = await jsonRequest({
                                    url: '/api/checkout/get-card-details',
                                    method: 'POST',
                                    data: {cardNumber: normalizedCardNumber}
                                });

                                setCardBrandCode(info.cardBrand);
                                setCardBrand(info.cardBrand);
                                setCardBrandLogo(info.cardBrandLogo);
                                setBankLogo(info.bankLogo);
                                setCardSchemaLogo(info.cardSchemaLogo);
                            } catch (error) {}
                        })();
                    } else {
                        setCardBrandCode('');
                        setCardBrand('');
                        setCardBrandLogo('');
                        setBankLogo('');
                        setCardSchemaLogo('');
                    }
                }

                setCardNumber(value);
            }
            if (field === 'cardHolder') {
                setCardHolder(value);
            }
            if (field === 'cardExpiry') {
                setCardExpiry(value);
            }
            if (field === 'cardCvv') {
                setCardCvv(value);
            }
        },
        [cardNumber, setCardBrandCode]
    );
    const onFocus = useCallback((field: string) => {
        setTimeout(() => {
            setFocussedField(field);
        }, 25);
    }, []);
    const onBlur = useCallback(() => {
        setFocussedField(undefined);
    }, []);

    return (
        <div className="card-container col-span-12 flex flex-col items-start justify-between gap-4 p-4 lg:col-span-9 lg:mt-4 lg:flex-row lg:p-8">
            <div>
                <CardBox
                    cardBrandLogo={cardBrandLogo}
                    bankLogo={bankLogo}
                    cardSchemaLogo={cardSchemaLogo}
                    cardNumber={cardNumber}
                    cardHolder={cardHolder}
                    cardExpiry={cardExpiry}
                    cardCvv={cardCvv}
                    focussedField={focussedField}
                />
            </div>

            <Form onChange={onChange} onFocus={onFocus} onBlur={onBlur} />
        </div>
    );
});

if (isDev) {
    PaymentCreditCard.displayName = 'PaymentCreditCard';
}

export default PaymentCreditCard;
