import {useTrans} from '@core/hooks';
import Seo from '@components/common/Seo';
import BalanceGroup from './BalanceGroup';
import PaymentCreditCard from './PaymentCreditCard';
import PaymentSummary from './PaymentSummary';
import CardInstallmentRates from './CardInstallmentRates';
import ApprovePayment from './PaymentCreditCard/ApprovePayment';
import {PaymentProvider} from './context';
import {useEffect} from 'react';

const NewPayment = () => {
    const t = useTrans();

    useEffect(() => {
        const fetchExchangeRates = async () => {
            const response = await fetch('/api/customers/exchange-rates', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'}
            });
            const data = await response.json();
            console.log(data);
        };
        fetchExchangeRates();
    }, []);

    return (
        <PaymentProvider>
            <Seo title={t('New Payment')} />

            <p className="mb-4 hidden text-xl font-medium xl:block">
                {t('New Payment')}
            </p>

            <BalanceGroup />

            <div className="grid grid-cols-12 gap-4">
                <PaymentSummary />
                <PaymentCreditCard />
            </div>

            <CardInstallmentRates />

            <ApprovePayment />
        </PaymentProvider>
    );
};

export default NewPayment;
