import {endpoint, erpClient, validator} from '@core/helpers/server';

export default endpoint.post(async (ctx, req, res) => {
    const {customer} = ctx;

    console.log('makePayment - Customer:', customer);
    console.log('makePayment - Request body:', req.body);

    const paymentSchema = validator.object({
        amount: validator.number().required(),
        amountUSD: validator.number().optional(),
        exchangeRate: validator.number().optional(),
        installmentCount: validator.number().required(),
        cardBrand: validator.string(),
        cardNumber: validator.string().required(),
        cardHolder: validator.string().required(),
        expireYear: validator.number().required(),
        expireMonth: validator.number().required(),
        cvv: validator.string().required()
    });

    const payload = await paymentSchema.validate(req.body.payload);

    console.log('makePayment - Validated payload:', payload);
    console.log('makePayment - Customer ID:', customer?.id);

    // Fix floating point precision issues
    const fixedAmount = Math.round(payload.amount * 100) / 100;
    const fixedAmountUSD = payload.amountUSD ? Math.round(payload.amountUSD * 100) / 100 : null;

    // Validate minimum amounts
    if (fixedAmount < 0.01 && (!fixedAmountUSD || fixedAmountUSD < 0.01)) {
        return res.status(400).json({
            status: 'error',
            code: 400,
            message: 'Payment amount must be at least 0.01'
        });
    }

    // Always use TL as primary currency for 3D Secure display
    // But keep dual currency information for backend processing
    let primaryAmount = fixedAmount;

    // If only USD is provided, convert to TL for display
    if (fixedAmount < 0.01 && fixedAmountUSD && fixedAmountUSD > 0 && payload.exchangeRate) {
        primaryAmount = fixedAmountUSD * payload.exchangeRate;
        primaryAmount = Math.round(primaryAmount * 100) / 100;
    }

    // Prepare dual currency payload for backend
    const backendPayload = {
        ...payload,
        customerId: customer?.id,
        // Use fixed amounts
        amount: primaryAmount, // Primary amount for installment calculation
        // Add dual currency fields
        tutar: fixedAmount, // TL amount
        tutar_sbp: fixedAmountUSD, // USD amount (SBP)
        currencyRate: payload.exchangeRate || 1,
        globalCurrencyRate: 1, // Default to 1, can be configured
        // Always use TRY as primary currency for 3D Secure display
        currencyId: 'TRY',
        evaluateAccountCurrency: false
    };

    console.log('makePayment - Backend payload:', backendPayload);

    try {
        const result = await erpClient.post('customers/make-payment', backendPayload);
        console.log('makePayment - ERP response:', result);
        return res.json(result);
    } catch (error) {
        console.error('makePayment - ERP error:', error);
        throw error;
    }
}, true); // isSecure = true
