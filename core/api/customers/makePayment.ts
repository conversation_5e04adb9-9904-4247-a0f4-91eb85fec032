import {endpoint, erpClient, validator} from '@core/helpers/server';

export default endpoint.post(async (ctx, req, res) => {
    const {customer} = ctx;

    console.log('makePayment - Customer:', customer);
    console.log('makePayment - Request body:', req.body);

    const paymentSchema = validator.object({
        amount: validator.number().required(),
        amountUSD: validator.number().optional(),
        exchangeRate: validator.number().optional(),
        installmentCount: validator.number().required(),
        cardBrand: validator.string(),
        cardNumber: validator.string().required(),
        cardHolder: validator.string().required(),
        expireYear: validator.number().required(),
        expireMonth: validator.number().required(),
        cvv: validator.string().required()
    });

    const payload = await paymentSchema.validate(req.body.payload);

    console.log('makePayment - Validated payload:', payload);
    console.log('makePayment - Customer ID:', customer?.id);

    // Fix floating point precision issues
    const fixedAmount = Math.round(payload.amount * 100) / 100;
    const fixedAmountUSD = payload.amountUSD ? Math.round(payload.amountUSD * 100) / 100 : null;

    // Validate minimum amounts
    if (fixedAmount < 0.01 && (!fixedAmountUSD || fixedAmountUSD < 0.01)) {
        return res.status(400).json({
            status: 'error',
            code: 400,
            message: 'Payment amount must be at least 0.01'
        });
    }

    // Determine primary amount and currency
    let primaryAmount = fixedAmount;
    let primaryCurrency = 'TRY';

    if (fixedAmountUSD && fixedAmountUSD > 0) {
        primaryAmount = fixedAmountUSD;
        primaryCurrency = 'USD';
    }

    // Prepare dual currency payload for backend
    const backendPayload = {
        ...payload,
        customerId: customer?.id,
        // Use fixed amounts
        amount: primaryAmount, // Primary amount for installment calculation
        // Add dual currency fields
        tutar: fixedAmountUSD, // TL amount
        tutar_sbp: fixedAmount, // USD amount (SBP)
        currencyRate: payload.exchangeRate || 1,
        globalCurrencyRate: 1, // Default to 1, can be configured
        // Determine which currency to use as primary
        currencyId: primaryCurrency,
        evaluateAccountCurrency: primaryCurrency === 'TRY'
    };

    console.log('makePayment - Backend payload:', backendPayload);

    try {
        const result = await erpClient.post('customers/make-payment', backendPayload);
        console.log('makePayment - ERP response:', result);
        return res.json(result);
    } catch (error) {
        console.error('makePayment - ERP error:', error);
        throw error;
    }
}, true); // isSecure = true
