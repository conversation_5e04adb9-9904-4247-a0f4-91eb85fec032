import {endpoint, erpClient, validator} from '@core/helpers/server';

export default endpoint.post(async (ctx, req, res) => {
    const {customer} = ctx;

    console.log('makePayment - Customer:', customer);
    console.log('makePayment - Request body:', req.body);

    const paymentSchema = validator.object({
        amount: validator.number().required(),
        amountUSD: validator.number().optional(),
        exchangeRate: validator.number().optional(),
        installmentCount: validator.number().required(),
        cardBrand: validator.string(),
        cardNumber: validator.string().required(),
        cardHolder: validator.string().required(),
        expireYear: validator.number().required(),
        expireMonth: validator.number().required(),
        cvv: validator.string().required()
    });

    const payload = await paymentSchema.validate(req.body.payload);

    console.log('makePayment - Validated payload:', payload);
    console.log('makePayment - Customer ID:', customer?.id);

    // Prepare dual currency payload for backend
    const backendPayload = {
        ...payload,
        customerId: customer?.id,
        // Add dual currency fields
        tutar: payload.amount, // TL amount
        tutar_sbp: payload.amountUSD, // USD amount (SBP)
        currencyRate: payload.exchangeRate || 1,
        globalCurrencyRate: 1, // Default to 1, can be configured
        // Determine which currency to use as primary
        currencyId: payload.amountUSD && payload.amountUSD > 0 ? 'USD' : 'TRY',
        evaluateAccountCurrency: payload.amountUSD && payload.amountUSD > 0
    };

    console.log('makePayment - Backend payload:', backendPayload);

    try {
        const result = await erpClient.post('customers/make-payment', backendPayload);
        console.log('makePayment - ERP response:', result);
        return res.json(result);
    } catch (error) {
        console.error('makePayment - ERP error:', error);
        throw error;
    }
}, true); // isSecure = true
