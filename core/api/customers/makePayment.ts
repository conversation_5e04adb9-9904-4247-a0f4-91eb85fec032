import {endpoint, erpClient, validator} from '@core/helpers/server';

export default endpoint.post(async (ctx, req, res) => {
    const {customer} = ctx;

    const paymentSchema = validator.object({
        amount: validator.number().required(),
        amountUSD: validator.number().optional(),
        exchangeRate: validator.number().optional(),
        installmentCount: validator.number().required(),
        cardBrand: validator.string(),
        cardNumber: validator.string().required(),
        cardHolder: validator.string().required(),
        expireYear: validator.number().required(),
        expireMonth: validator.number().required(),
        cvv: validator.string().required()
    });

    const payload = await paymentSchema.validate(req.body.payload);

    // Prepare dual currency payload for backend
    const backendPayload = {
        ...payload,
        customerId: customer.id,
        // Add dual currency fields
        tutar: payload.amount, // TL amount
        tutar_sbp: payload.amountUSD, // USD amount (SBP)
        currencyRate: payload.exchangeRate || 1,
        globalCurrencyRate: 1, // Default to 1, can be configured
        // Determine which currency to use as primary
        currencyId: payload.amountUSD && payload.amountUSD > 0 ? 'USD' : 'TRY',
        evaluateAccountCurrency: payload.amountUSD && payload.amountUSD > 0
    };

    return res.json(
        await erpClient.post('customers/make-payment', backendPayload)
    );
}, true); // isSecure = true
