// Updated make-payment backend with dual currency support
// Bu dosya mevcut make-payment backend'inizi dual currency sistemine göre güncellenmiş halidir

import _ from 'lodash';
import axios from 'axios';
import {trim} from 'framework/helpers';
import {findInstallment} from '../checkout/utils';
import {integrations} from '../../../finance/methods/online-pos-receipts';

async function processDualCurrencyPayment(payload, app, company) {
    const items = [];
    const slips = [];
    
    if (payload.documentType === 'pos') {
        const pos = await app.collection('accounting.pos').get(payload.posId);
        console.log('Processing POS payment with dual currency support');

        let currencyId = payload.currencyId;
        if (!!payload.evaluateAccountCurrency) {
            currencyId = pos.currencyId;
        }

        // Dual currency handling - determine which amount to use
        let finalAmount = payload.total;
        let finalInstallmentAmount = payload.installmentAmount;
        let isUsingForeignCurrency = false;
        
        // Check if we have both TL and USD amounts
        if (payload.tutar && payload.tutar_sbp) {
            // Determine primary currency based on transaction settings
            if (currencyId !== company.currencyId) {
                // Use foreign currency (USD/SBP) as primary
                finalAmount = payload.tutar_sbp;
                finalInstallmentAmount = payload.tutar_sbp / payload.installmentCount;
                isUsingForeignCurrency = true;
            } else {
                // Use domestic currency (TL) as primary
                finalAmount = payload.tutar;
                finalInstallmentAmount = payload.tutar / payload.installmentCount;
                isUsingForeignCurrency = false;
            }
        } else if (payload.tutar_sbp && !payload.tutar) {
            // Only USD amount provided
            finalAmount = payload.tutar_sbp;
            finalInstallmentAmount = payload.tutar_sbp / payload.installmentCount;
            isUsingForeignCurrency = true;
        } else {
            // Only TL amount provided or fallback to original logic
            finalAmount = payload.tutar || payload.total;
            finalInstallmentAmount = payload.installmentAmount;
            isUsingForeignCurrency = false;
        }

        if (pos.splitCreditCardSlips) {
            const startDate = app.datetime.fromJSDate(entry.issueDate).startOf('day');

            for (let i = 1; i <= payload.installmentCount; i++) {
                const slip = {};
                let dueDate = null;

                // Calculate due date (existing logic)
                if (pos.paymentOnSpecificDate) {
                    const now = startDate.plus({months: i - 1});
                    const cutoffDate = pos.cutoffDate;

                    if (now.day > cutoffDate) {
                        dueDate = now.plus({months: 1}).set({day: cutoffDate}).toJSDate();
                    } else {
                        dueDate = now.set({day: cutoffDate}).toJSDate();
                    }
                } else {
                    const installment = pos.installmentPayments.find(p => p.installment === i);
                    dueDate = startDate.plus({days: installment.refund || 0}).toJSDate();
                }

                // Create journal item with dual currency support
                let journalItem = {};
                journalItem.accountId = journal.debitAccountId;
                journalItem.partnerId = partner._id;
                journalItem.description = payload.description;
                journalItem.dueDate = dueDate;
                journalItem.branchId = payload.branchId;
                journalItem.currencyId = currencyId;
                journalItem.financialProjectId = payload.financialProjectId;
                journalItem.cashFlowItemId = payload.cashFlowItemId;
                
                // Enhanced currency conversion logic
                if (currencyId !== company.currencyId) {
                    // Foreign currency transaction
                    journalItem.debitFC = finalInstallmentAmount;
                    journalItem.debit = finalInstallmentAmount * payload.currencyRate * payload.globalCurrencyRate;
                    
                    // Store both amounts for reference and reporting
                    journalItem.originalAmountTL = payload.tutar ? (payload.tutar / payload.installmentCount) : null;
                    journalItem.originalAmountUSD = payload.tutar_sbp ? (payload.tutar_sbp / payload.installmentCount) : null;
                    journalItem.exchangeRateUsed = payload.currencyRate;
                    journalItem.globalCurrencyRate = payload.globalCurrencyRate;
                } else {
                    // Domestic currency transaction
                    if (payload.evaluateAccountCurrency) {
                        journalItem.debit = finalInstallmentAmount * payload.currencyRate;
                    } else {
                        journalItem.debit = finalInstallmentAmount;
                    }
                    
                    // Store both amounts for reference
                    journalItem.originalAmountTL = payload.tutar ? (payload.tutar / payload.installmentCount) : finalInstallmentAmount;
                    journalItem.originalAmountUSD = payload.tutar_sbp ? (payload.tutar_sbp / payload.installmentCount) : null;
                    journalItem.exchangeRateUsed = payload.currencyRate;
                }
                
                items.push(journalItem);

                // Create slip with dual currency information
                slip.status = 'open';
                slip.documentNo = payload.documentNo;
                slip.partnerType = payload.partnerType;
                slip.partnerId = payload.partnerId;
                slip.contactPersonId = payload.contactPersonId;
                slip.journalId = payload.journalId;
                slip.paymentAccountId = payload.paymentAccountId;
                slip.currencyId = currencyId;
                slip.currencyRate = payload.currencyRate * payload.globalCurrencyRate;
                slip.recordDate = payload.recordDate;
                slip.issueDate = payload.issueDate;
                slip.dueDate = dueDate;
                slip.reference = payload.reference;
                slip.description = payload.description;
                slip.branchId = payload.branchId;
                
                // Enhanced slip amounts with dual currency
                slip.amount = payload.tutar || payload.baseTotal;
                slip.amountUSD = payload.tutar_sbp || null;
                slip.installmentNo = i;
                slip.installmentCount = payload.installmentCount;
                slip.plusInstallmentCount = payload.plusInstallmentCount;
                slip.installmentAmount = finalInstallmentAmount;
                slip.dueDifference = payload.dueDifference;
                slip.total = finalAmount;
                
                // Card information
                slip.partnerCreditCardId = payload.partnerCreditCardId;
                slip.cardBrand = payload.cardBrand;
                slip.cardHolder = payload.cardHolder;
                slip.cardNumber = payload.cardNumber;
                slip.expireMonth = payload.expireMonth;
                slip.expireYear = payload.expireYear;
                slip.cvv = payload.cvv;
                slip.issuedBy = payload.issuedBy;
                slip.tagIds = payload.tagIds || [];
                slip.financialProjectId = payload.financialProjectId;
                slip.cashFlowItemId = payload.cashFlowItemId;

                // Apply currency evaluation if needed
                if (payload.evaluateAccountCurrency) {
                    slip.amount = (payload.tutar || payload.baseTotal) * payload.currencyRate;
                    slip.installmentAmount = finalInstallmentAmount * payload.currencyRate;
                    slip.dueDifference = payload.dueDifference * payload.currencyRate;
                    slip.total = finalAmount * payload.currencyRate;
                }

                slips.push(slip);
            }
        } else {
            // Single payment logic with dual currency
            let journalItem = {};
            journalItem.accountId = journal.debitAccountId;
            journalItem.partnerId = partner._id;
            journalItem.description = payload.description;
            journalItem.branchId = payload.branchId;
            journalItem.currencyId = currencyId;
            journalItem.financialProjectId = payload.financialProjectId;
            journalItem.cashFlowItemId = payload.cashFlowItemId;
            
            if (currencyId !== company.currencyId) {
                journalItem.debitFC = finalAmount;
                journalItem.debit = finalAmount * payload.currencyRate * payload.globalCurrencyRate;
                journalItem.originalAmountTL = payload.tutar || null;
                journalItem.originalAmountUSD = payload.tutar_sbp || null;
                journalItem.exchangeRateUsed = payload.currencyRate;
            } else {
                if (payload.evaluateAccountCurrency) {
                    journalItem.debit = finalAmount * payload.currencyRate;
                } else {
                    journalItem.debit = finalAmount;
                }
                journalItem.originalAmountTL = payload.tutar || finalAmount;
                journalItem.originalAmountUSD = payload.tutar_sbp || null;
                journalItem.exchangeRateUsed = payload.currencyRate;
            }
            
            items.push(journalItem);

            // Single slip with dual currency
            const slip = {};
            slip.status = 'open';
            slip.documentNo = payload.documentNo;
            slip.partnerType = payload.partnerType;
            slip.partnerId = payload.partnerId;
            slip.contactPersonId = payload.contactPersonId;
            slip.journalId = payload.journalId;
            slip.paymentAccountId = payload.paymentAccountId;
            slip.currencyId = currencyId;
            slip.currencyRate = payload.currencyRate * payload.globalCurrencyRate;
            slip.recordDate = payload.recordDate;
            slip.issueDate = payload.issueDate;
            slip.dueDate = payload.dueDate;
            slip.reference = payload.reference;
            slip.description = payload.description;
            slip.branchId = payload.branchId;
            
            // Dual currency amounts
            slip.amount = payload.tutar || payload.baseTotal;
            slip.amountUSD = payload.tutar_sbp || null;
            slip.installmentNo = 1;
            slip.installmentCount = payload.installmentCount;
            slip.plusInstallmentCount = payload.plusInstallmentCount;
            slip.installmentAmount = finalInstallmentAmount;
            slip.dueDifference = payload.dueDifference;
            slip.total = finalAmount;
            
            // Card information
            slip.partnerCreditCardId = payload.partnerCreditCardId;
            slip.cardBrand = payload.cardBrand;
            slip.cardHolder = payload.cardHolder;
            slip.cardNumber = payload.cardNumber;
            slip.expireMonth = payload.expireMonth;
            slip.expireYear = payload.expireYear;
            slip.cvv = payload.cvv;
            slip.issuedBy = payload.issuedBy;
            slip.tagIds = payload.tagIds || [];
            slip.financialProjectId = payload.financialProjectId;
            slip.cashFlowItemId = payload.cashFlowItemId;

            if (payload.evaluateAccountCurrency) {
                slip.amount = (payload.tutar || payload.baseTotal) * payload.currencyRate;
                slip.installmentAmount = finalInstallmentAmount * payload.currencyRate;
                slip.dueDifference = payload.dueDifference * payload.currencyRate;
                slip.total = finalAmount * payload.currencyRate;
            }

            slips.push(slip);
        }
    } else {
        // Non-POS transaction logic with dual currency support
        let currencyId = payload.currencyId;
        if (!!payload.evaluateAccountCurrency) {
            currencyId = journal.currencyId ?? company.currencyId;
        }

        let journalItem = {};
        journalItem.accountId = journal.debitAccountId;
        journalItem.partnerId = partner._id;
        journalItem.description = payload.description;
        journalItem.branchId = payload.branchId;
        journalItem.currencyId = currencyId;
        journalItem.scope = payload.scope || '1';
        journalItem.financialProjectId = payload.financialProjectId;
        journalItem.cashFlowItemId = payload.cashFlowItemId;
        
        // Determine final amount for non-POS transactions
        let finalAmount = payload.tutar || payload.total;
        if (payload.tutar_sbp && currencyId !== company.currencyId) {
            finalAmount = payload.tutar_sbp;
        }
        
        if (currencyId !== company.currencyId) {
            journalItem.debitFC = finalAmount;
            journalItem.debit = finalAmount * payload.currencyRate * payload.globalCurrencyRate;
            journalItem.originalAmountTL = payload.tutar || null;
            journalItem.originalAmountUSD = payload.tutar_sbp || null;
        } else {
            if (payload.evaluateAccountCurrency) {
                journalItem.debit = finalAmount * payload.currencyRate;
            } else {
                journalItem.debit = finalAmount;
            }
            journalItem.originalAmountTL = payload.tutar || finalAmount;
            journalItem.originalAmountUSD = payload.tutar_sbp || null;
        }
        
        items.push(journalItem);
    }

    return { items, slips };
}

module.exports = { processDualCurrencyPayment };
